# Share 功能實現說明

## 功能概述

在筆記功能中新增了一個名為 "Share" 的根目錄，功能類似於 Google 雲端硬碟的「與我共用」區域。用戶現在可以在 "Root"（我的資料夾）和 "Share"（共用資料夾）之間切換。

## 實現的功能

### 1. 雙根目錄結構
- **Root 目錄**: 顯示用戶擁有的資料夾和筆記（user_id == 當前用戶）
- **Share 目錄**: 顯示其他用戶與我共用的資料夾和筆記（user_id != 當前用戶 且 有 read 權限）

### 2. 權限邏輯
- 公開資料夾（access_control = null）會出現在 Share 目錄中
- 只顯示用戶有讀取權限的共用內容
- 完整的權限檢查機制，支援用戶和群組級別的權限控制

### 3. 用戶界面
- 在資料夾導航中新增了 "Share" 選項，使用 Share 圖標
- 支援在兩個根目錄之間切換
- 面包屑導航會顯示當前所在的根目錄類型
- 空狀態提示會根據當前根目錄類型顯示不同的訊息

## 修改的檔案

### 後端修改

#### 1. `backend/open_webui/models/note_folders.py`
- 新增 `get_shared_folders_by_user_id()` 方法：獲取與用戶共用的資料夾
- 新增 `get_shared_folder_tree()` 方法：獲取共用資料夾的樹狀結構
- 實現了完整的權限檢查邏輯

#### 2. `backend/open_webui/routers/note_folders.py`
- 新增 `GET /api/v1/notes/folders/shared` 端點：獲取共用資料夾列表
- 新增 `GET /api/v1/notes/folders/shared/tree` 端點：獲取共用資料夾樹狀結構
- 新增 `GET /api/v1/notes/folders/shared/notes` 端點：獲取共用區域中的筆記

#### 3. `backend/open_webui/models/notes.py`
- 修改 `get_notes_by_folder_id()` 方法，支援 user_id 為 None 的情況，用於獲取所有筆記

### 前端修改

#### 1. `src/lib/apis/note-folders/index.ts`
- 新增 `getSharedNoteFolderTree()` 函數：獲取共用資料夾樹
- 新增 `getNotesInShared()` 函數：獲取共用區域中的筆記

#### 2. `src/lib/components/notes/FolderTree.svelte`
- 支援雙根目錄結構（Root/Shared）
- 新增 `selectedRootType` 屬性來跟蹤當前選中的根目錄類型
- 修改初始化邏輯，同時載入用戶擁有的資料夾和共用資料夾
- 新增根目錄選擇按鈕，支援在 Root 和 Shared 之間切換
- 根據當前根目錄類型顯示對應的資料夾樹

#### 3. `src/lib/components/notes/NotesWithFolders.svelte`
- 新增 `selectedRootType` 變數來跟蹤當前根目錄類型
- 修改 `loadNotes()` 函數，根據根目錄類型載入不同的筆記
- 修改 `handleFolderSelected()` 函數，支援根目錄類型切換
- 更新面包屑導航，顯示正確的根目錄名稱

## API 端點

### 新增的 API 端點

1. **GET** `/api/v1/notes/folders/shared`
   - 功能：獲取與用戶共用的資料夾列表
   - 回應：`List[NoteFolderModel]`

2. **GET** `/api/v1/notes/folders/shared/tree`
   - 功能：獲取用戶的共用資料夾樹狀結構
   - 回應：`List[NoteFolderTreeNode]`

3. **GET** `/api/v1/notes/folders/shared/notes`
   - 功能：獲取共用區域中的筆記（沒有資料夾的共用筆記）
   - 回應：`List[NoteUserResponse]`

## 權限控制機制

### access_control 欄位結構
```json
{
  "read": {
    "group_ids": ["group_id1", "group_id2"],
    "user_ids": ["user_id1", "user_id2"]
  },
  "write": {
    "group_ids": ["group_id1", "group_id2"],
    "user_ids": ["user_id1", "user_id2"]
  }
}
```

### 權限邏輯
- `null`: 公開存取，所有用戶都可讀取
- `{}`: 私人存取，只有擁有者可存取
- 自定義權限：可指定特定用戶或群組的讀寫權限

## 使用方式

1. **切換根目錄**: 在左側資料夾樹中點擊 "Root" 或 "Shared" 按鈕
2. **瀏覽共用內容**: 選擇 "Shared" 後，可以看到其他用戶與您共用的資料夾和筆記
3. **權限限制**: 在 Share 目錄中，您只能查看有讀取權限的內容，無法編輯或刪除

## 注意事項

1. 共用資料夾的層級結構會根據用戶的權限進行過濾
2. 如果父資料夾沒有權限但子資料夾有權限，子資料夾會顯示為根級資料夾
3. 筆記數量統計只計算用戶有權限查看的筆記
4. 目前實現中，i18n 的使用可能需要進一步調整以支援多語言

## 測試

可以使用提供的 `test_share_feature.py` 腳本來測試後端 API 的功能。需要先獲取有效的認證 token。
