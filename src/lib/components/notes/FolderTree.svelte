<script lang="ts">
	import { createEventDispatcher, getContext } from 'svelte';
	import { toast } from 'svelte-sonner';
	import {
		getNoteFolderTree,
		getSharedNoteFolderTree,
		createNoteFolder,
		updateNoteFolder,
		deleteNoteFolder,
		type NoteFolderTreeNode
	} from '$lib/apis/note-folders';

	// Icons
	import ChevronRight from '$lib/components/icons/ChevronRight.svelte';
	import ChevronDown from '$lib/components/icons/ChevronDown.svelte';
	import Folder from '$lib/components/icons/Folder.svelte';
	import FolderOpen from '$lib/components/icons/FolderOpen.svelte';
	import Plus from '$lib/components/icons/Plus.svelte';

	import Pencil from '$lib/components/icons/Pencil.svelte';
	import Trash from '$lib/components/icons/GarbageBin.svelte';
	import Share from '$lib/components/icons/Share.svelte';

	// Components
	import CreateFolderModal from './CreateFolderModal.svelte';
	import EditFolderModal from './EditFolderModal.svelte';
	import DeleteFolderModal from './DeleteFolderModal.svelte';

	const i18n = getContext('i18n');
	const dispatch = createEventDispatcher();

	export let selectedFolderId: string | null = null;
	export let expandedFolders: Set<string> = new Set();
	export let selectedRootType: 'root' | 'shared' = 'root'; // 新增：選中的根目錄類型

	// 支援遞歸渲染的 props
	export let folder: NoteFolderTreeNode | null = null;
	export let level: number = 0;

	let folderTree: NoteFolderTreeNode[] = [];
	let sharedFolderTree: NoteFolderTreeNode[] = [];
	let loading = false;
	let showCreateModal = false;
	let showEditModal = false;
	let showDeleteModal = false;
	let currentFolder: NoteFolderTreeNode | null = null;
	let createParentId: string | null = null;

	// 初始化資料夾樹
	const init = async () => {
		loading = true;
		try {
			// 同時載入用戶擁有的資料夾和共用資料夾
			const [ownedFolders, sharedFolders] = await Promise.all([
				getNoteFolderTree(localStorage.token),
				getSharedNoteFolderTree(localStorage.token)
			]);
			folderTree = ownedFolders;
			sharedFolderTree = sharedFolders;
		} catch (error) {
			toast.error(`${error}`);
		} finally {
			loading = false;
		}
	};

	// 切換資料夾展開狀態
	const toggleFolder = (folderId: string) => {
		if (expandedFolders.has(folderId)) {
			expandedFolders.delete(folderId);
		} else {
			expandedFolders.add(folderId);
		}
		expandedFolders = expandedFolders;
	};

	// 選擇資料夾
	const selectFolder = (folderId: string | null) => {
		selectedFolderId = folderId;
		dispatch('folderSelected', { folderId, rootType: selectedRootType });
	};

	// 選擇根目錄類型
	const selectRootType = (rootType: 'root' | 'shared') => {
		selectedRootType = rootType;
		selectedFolderId = null; // 切換根目錄時清空選中的資料夾
		dispatch('rootTypeSelected', { rootType });
		dispatch('folderSelected', { folderId: null, rootType });
	};

	// 顯示創建資料夾對話框
	const showCreateFolderDialog = (parentId: string | null = null) => {
		createParentId = parentId;
		showCreateModal = true;
	};

	// 創建資料夾
	const handleCreateFolder = async (event) => {
		const folderData = event.detail;
		try {
			await createNoteFolder(localStorage.token, folderData);
			toast.success($i18n.t('Folder created successfully'));
			await init();
		} catch (error) {
			toast.error(`${error}`);
		}
	};

	// 顯示編輯資料夾對話框
	const showEditFolderDialog = (folder: NoteFolderTreeNode) => {
		currentFolder = folder;
		showEditModal = true;
	};

	// 編輯資料夾
	const handleEditFolder = async (event) => {
		const { folder, updateData } = event.detail;
		try {
			await updateNoteFolder(localStorage.token, folder.id, updateData);
			toast.success($i18n.t('Folder updated successfully'));
			await init();
		} catch (error) {
			toast.error(`${error}`);
		}
	};

	// 顯示刪除資料夾對話框
	const showDeleteFolderDialog = (folder: NoteFolderTreeNode) => {
		currentFolder = folder;
		showDeleteModal = true;
	};

	// 刪除資料夾
	const handleDeleteFolder = async (event) => {
		const folder = event.detail;
		try {
			await deleteNoteFolder(localStorage.token, folder.id);
			toast.success($i18n.t('Folder deleted successfully'));
			await init();
		} catch (error) {
			toast.error(`${error}`);
		}
	};

	// 處理資料夾切換事件
	const handleToggleFolder = (event) => {
		toggleFolder(event.detail.folderId);
	};

	// 處理資料夾選擇事件
	const handleSelectFolder = (event) => {
		selectFolder(event.detail.folderId);
	};

	// 處理創建子資料夾事件
	const handleCreateSubfolder = (event) => {
		showCreateFolderDialog(event.detail.parentId);
	};

	// 處理編輯資料夾事件
	const handleEditFolderEvent = (event) => {
		showEditFolderDialog(event.detail.folder);
	};

	// 處理刪除資料夾事件
	const handleDeleteFolderEvent = (event) => {
		showDeleteFolderDialog(event.detail.folder);
	};

	// 初始化（只在根組件執行）
	if (!folder) {
		init();
	}
</script>

{#if folder}
	<!-- 遞歸渲染單個資料夾 -->
	{@const isExpanded = expandedFolders.has(folder.id)}
	{@const isSelected = selectedFolderId === folder.id}
	{@const hasChildren = folder.children && folder.children.length > 0}

	<div class="folder-node" style="padding-left: {level * 16 + 8}px">
		<div class="flex items-center group hover:bg-gray-100 dark:hover:bg-gray-700 rounded px-2 py-1">
			<!-- 展開/收縮按鈕 -->
			{#if hasChildren}
				<button
					type="button"
					class="p-0.5 mr-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
					on:click={() => dispatch('toggle', { folderId: folder.id })}
				>
					{#if isExpanded}
						<ChevronDown className="w-3 h-3" />
					{:else}
						<ChevronRight className="w-3 h-3" />
					{/if}
				</button>
			{:else}
				<div class="w-4 h-4 mr-1"></div>
			{/if}

			<!-- 資料夾圖標 -->
			<div class="mr-2" style="color: {folder.color || '#3b82f6'}">
				{#if isExpanded && hasChildren}
					<FolderOpen className="w-4 h-4" />
				{:else}
					<Folder className="w-4 h-4" />
				{/if}
			</div>

			<!-- 資料夾名稱 -->
			<button
				type="button"
				class="flex-1 text-left text-sm {isSelected
					? 'text-blue-700 dark:text-blue-300 font-medium'
					: 'text-gray-700 dark:text-gray-300'}"
				on:click={() => dispatch('select', { folderId: folder.id })}
			>
				{folder.name}
			</button>

			<!-- 筆記數量 -->
			{#if folder.total_note_count > 0}
				<span class="text-xs text-gray-400 mr-2">
					{folder.total_note_count}
				</span>
			{/if}

			<!-- 操作按鈕 -->
			<div class="opacity-0 group-hover:opacity-100 flex items-center space-x-1">
				<button
					type="button"
					class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded"
					on:click={() => dispatch('createSubfolder', { parentId: folder.id })}
					title={$i18n.t('Create Subfolder')}
				>
					<Plus className="w-3 h-3" />
				</button>
				<button
					type="button"
					class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded"
					on:click={() => dispatch('editFolder', { folder })}
					title={$i18n.t('Edit Folder')}
				>
					<Pencil className="w-3 h-3" />
				</button>
				<button
					type="button"
					class="p-1 text-gray-400 hover:text-red-600 dark:hover:text-red-400 rounded"
					on:click={() => dispatch('deleteFolder', { folder })}
					title={$i18n.t('Delete Folder')}
				>
					<Trash className="w-3 h-3" />
				</button>
			</div>
		</div>

		<!-- 遞歸渲染子資料夾 -->
		{#if isExpanded && hasChildren}
			{#each folder.children as childFolder (childFolder.id)}
				<svelte:self
					folder={childFolder}
					level={level + 1}
					{selectedFolderId}
					{expandedFolders}
					on:toggle
					on:select
					on:createSubfolder
					on:editFolder
					on:deleteFolder
				/>
			{/each}
		{/if}
	</div>
{:else}
	<!-- 根組件渲染 -->
	<div class="folder-tree">
		<!-- 頭部 -->
		<div
			class="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700"
		>
			<h3 class="text-sm font-medium text-gray-900 dark:text-white">
				{$i18n.t('Folders')}
			</h3>
			<button
				type="button"
				class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded"
				on:click={() => showCreateFolderDialog()}
				title={$i18n.t('Create Folder')}
			>
				<Plus className="w-4 h-4" />
			</button>
		</div>

		<!-- 根目錄選項 -->
		<div class="p-2 space-y-1">
			<!-- Root 根目錄 -->
			<button
				type="button"
				class="w-full flex items-center px-2 py-1.5 text-sm rounded hover:bg-gray-100 dark:hover:bg-gray-700 {selectedFolderId ===
					null && selectedRootType === 'root'
					? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
					: 'text-gray-700 dark:text-gray-300'}"
				on:click={() => selectRootType('root')}
			>
				<Folder className="w-4 h-4 mr-2" />
				<span class="flex-1 text-left">Root</span>
			</button>

			<!-- Share 共用目錄 -->
			<button
				type="button"
				class="w-full flex items-center px-2 py-1.5 text-sm rounded hover:bg-gray-100 dark:hover:bg-gray-700 {selectedFolderId ===
					null && selectedRootType === 'shared'
					? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
					: 'text-gray-700 dark:text-gray-300'}"
				on:click={() => selectRootType('shared')}
			>
				<Share className="w-4 h-4 mr-2" />
				<span class="flex-1 text-left">Shared</span>
			</button>
		</div>

		<!-- 資料夾樹 -->
		<div class="flex-1 overflow-y-auto">
			{#if loading}
				<div class="flex items-center justify-center p-4">
					<div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
				</div>
			{:else}
				{@const currentTree = selectedRootType === 'root' ? folderTree : sharedFolderTree}
				{#if currentTree.length === 0}
					<div class="p-4 text-center text-gray-500 dark:text-gray-400 text-sm">
						{selectedRootType === 'root' ? 'No folders yet' : 'No shared folders'}
					</div>
				{:else}
					{#each currentTree as rootFolder (rootFolder.id)}
						<svelte:self
							folder={rootFolder}
							level={0}
							{selectedFolderId}
							{expandedFolders}
							{selectedRootType}
							on:toggle={handleToggleFolder}
							on:select={handleSelectFolder}
							on:createSubfolder={handleCreateSubfolder}
							on:editFolder={handleEditFolderEvent}
							on:deleteFolder={handleDeleteFolderEvent}
						/>
					{/each}
				{/if}
			{/if}
		</div>
	</div>
{/if}

<!-- 彈窗組件 -->
<CreateFolderModal
	bind:show={showCreateModal}
	parentId={createParentId}
	on:confirm={handleCreateFolder}
/>

<EditFolderModal bind:show={showEditModal} folder={currentFolder} on:confirm={handleEditFolder} />

<DeleteFolderModal
	bind:show={showDeleteModal}
	folder={currentFolder}
	on:confirm={handleDeleteFolder}
/>

<style>
	.folder-tree {
		display: flex;
		flex-direction: column;
		height: 100%;
		background-color: white;
		border: 1px solid #e5e7eb;
		border-radius: 0.5rem;
	}

	:global(.dark) .folder-tree {
		background-color: #1f2937;
		border-color: #374151;
	}

	.folder-node {
		position: relative;
	}
</style>
