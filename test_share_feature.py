#!/usr/bin/env python3
"""
測試 Share 功能的簡單腳本
"""

import requests
import json

# 配置
BASE_URL = "http://localhost:8080"
API_BASE = f"{BASE_URL}/api/v1"

def test_shared_folder_apis():
    """測試共用資料夾相關的 API"""
    
    # 這裡需要一個有效的 token，實際使用時需要先登入獲取
    token = "your_token_here"
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print("測試共用資料夾 API...")
    
    # 測試獲取共用資料夾列表
    try:
        response = requests.get(f"{API_BASE}/notes/folders/shared", headers=headers)
        print(f"獲取共用資料夾列表: {response.status_code}")
        if response.status_code == 200:
            shared_folders = response.json()
            print(f"共用資料夾數量: {len(shared_folders)}")
        else:
            print(f"錯誤: {response.text}")
    except Exception as e:
        print(f"請求失敗: {e}")
    
    # 測試獲取共用資料夾樹
    try:
        response = requests.get(f"{API_BASE}/notes/folders/shared/tree", headers=headers)
        print(f"獲取共用資料夾樹: {response.status_code}")
        if response.status_code == 200:
            shared_tree = response.json()
            print(f"共用資料夾樹根節點數量: {len(shared_tree)}")
        else:
            print(f"錯誤: {response.text}")
    except Exception as e:
        print(f"請求失敗: {e}")
    
    # 測試獲取共用筆記
    try:
        response = requests.get(f"{API_BASE}/notes/folders/shared/notes", headers=headers)
        print(f"獲取共用筆記: {response.status_code}")
        if response.status_code == 200:
            shared_notes = response.json()
            print(f"共用筆記數量: {len(shared_notes)}")
        else:
            print(f"錯誤: {response.text}")
    except Exception as e:
        print(f"請求失敗: {e}")

def print_api_endpoints():
    """打印新增的 API 端點"""
    print("\n新增的 API 端點:")
    print("1. GET /api/v1/notes/folders/shared - 獲取共用資料夾列表")
    print("2. GET /api/v1/notes/folders/shared/tree - 獲取共用資料夾樹狀結構")
    print("3. GET /api/v1/notes/folders/shared/notes - 獲取共用區域中的筆記")
    print("\n前端修改:")
    print("1. FolderTree.svelte - 支援雙根目錄結構 (Root/Shared)")
    print("2. NotesWithFolders.svelte - 支援根目錄類型切換")
    print("3. note-folders/index.ts - 新增共用資料夾 API 函數")

if __name__ == "__main__":
    print("Share 功能實現測試")
    print("=" * 50)
    
    print_api_endpoints()
    
    print("\n注意: 要測試 API，請先:")
    print("1. 啟動 Open WebUI 服務")
    print("2. 登入並獲取有效的 token")
    print("3. 修改此腳本中的 token 變數")
    print("4. 運行: python test_share_feature.py")
    
    # 如果有 token，可以取消註釋下面這行來測試
    # test_shared_folder_apis()
